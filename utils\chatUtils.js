/**
 * Utility functions for handling WhatsApp chat operations
 */

/**
 * Check if a chat is a group chat using multiple detection methods
 * @param {Object} chat - The chat object
 * @returns {boolean} - True if it's a group chat
 */
function isGroupChat(chat) {
    // Primary method: Check if ID ends with @g.us (group pattern)
    if (chat.id && chat.id._serialized && chat.id._serialized.endsWith('@g.us')) {
        return true;
    }
    
    // Secondary method: Check isGroup property if available
    if (chat.isGroup === true) {
        return true;
    }
    
    // Tertiary method: Check if participants exist (groups have participants)
    if (chat.participants && Array.isArray(chat.participants) && chat.participants.length > 0) {
        return true;
    }
    
    return false;
}

/**
 * Get group participants with retry mechanism
 * @param {Object} client - WhatsApp client
 * @param {Object} chat - The chat object
 * @param {number} maxRetries - Maximum number of retries
 * @returns {Array} - Array of participants or empty array
 */
async function getGroupParticipants(client, chat, maxRetries = 3) {
    let participants = [];
    
    // First try: Use existing participants from chat object
    if (chat.participants && Array.isArray(chat.participants) && chat.participants.length > 0) {
        console.log(`Found ${chat.participants.length} participants from chat object`);
        return chat.participants;
    }
    
    // Second try: Refresh chat data
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`Attempt ${attempt}: Getting fresh chat data...`);
            const freshChat = await client.getChatById(chat.id._serialized);
            
            if (freshChat.participants && Array.isArray(freshChat.participants) && freshChat.participants.length > 0) {
                console.log(`Found ${freshChat.participants.length} participants after refresh (attempt ${attempt})`);
                return freshChat.participants;
            }
            
            // Wait before next attempt
            if (attempt < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        } catch (error) {
            console.log(`Error getting fresh chat data (attempt ${attempt}):`, error.message);
            
            // Wait before next attempt
            if (attempt < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }
    
    // Third try: Get all chats and find the specific one
    try {
        console.log('Trying to find chat in all chats...');
        const allChats = await client.getChats();
        const targetChat = allChats.find(c => c.id._serialized === chat.id._serialized);
        
        if (targetChat && targetChat.participants && Array.isArray(targetChat.participants)) {
            console.log(`Found ${targetChat.participants.length} participants from all chats`);
            return targetChat.participants;
        }
    } catch (error) {
        console.log('Error getting participants from all chats:', error.message);
    }
    
    console.log('Could not retrieve participants after all attempts');
    return [];
}

/**
 * Check if a user is admin in a group
 * @param {Object} client - WhatsApp client
 * @param {Object} chat - The chat object
 * @param {Object} contact - The contact object
 * @returns {boolean} - True if user is admin
 */
async function isUserAdmin(client, chat, contact) {
    try {
        // Only check admin status in groups
        if (!isGroupChat(chat)) {
            return false;
        }
        
        const participants = await getGroupParticipants(client, chat);
        
        if (participants.length === 0) {
            console.log('No participants found, cannot determine admin status');
            return false;
        }
        
        const participant = participants.find(p => p.id._serialized === contact.id._serialized);
        const isAdmin = participant && participant.isAdmin;
        
        console.log(`User ${contact.pushname || contact.number} admin status: ${isAdmin}`);
        return isAdmin;
        
    } catch (error) {
        console.error('Error checking admin status:', error);
        return false;
    }
}

/**
 * Get chat info with enhanced detection
 * @param {Object} client - WhatsApp client
 * @param {Object} message - The message object
 * @returns {Object} - Enhanced chat info
 */
async function getChatInfo(client, message) {
    try {
        const chat = await message.getChat();
        const contact = await message.getContact();
        
        const chatInfo = {
            chat: chat,
            contact: contact,
            isGroup: isGroupChat(chat),
            participants: [],
            isUserAdmin: false
        };
        
        if (chatInfo.isGroup) {
            chatInfo.participants = await getGroupParticipants(client, chat);
            chatInfo.isUserAdmin = await isUserAdmin(client, chat, contact);
        }
        
        console.log('Chat info:', {
            id: chat.id._serialized,
            name: chat.name,
            isGroup: chatInfo.isGroup,
            participantCount: chatInfo.participants.length,
            isUserAdmin: chatInfo.isUserAdmin
        });
        
        return chatInfo;
        
    } catch (error) {
        console.error('Error getting chat info:', error);
        throw error;
    }
}

module.exports = {
    isGroupChat,
    getGroupParticipants,
    isUserAdmin,
    getChatInfo
};
