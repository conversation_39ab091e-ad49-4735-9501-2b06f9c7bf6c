# Discord Bot Configuration
# Note: For full functionality, enable MESSAGE CONTENT intent in Discord Developer Portal
# See DISCORD_SETUP.md for instructions
DISCORD_TOKEN=your_discord_token
DISCORD_CHANNEL_ID=your_discord_channel_id

# Discord OAuth2 Configuration (for web authentication)
DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret
DISCORD_REDIRECT_URI=http://localhost:3000/auth/discord/callback

# WhatsApp Bot Configuration
WHATSAPP_CLIENT_ID=your_whatsapp_client_id
WHATSAPP_GROUP_IDS=<EMAIL>,<EMAIL>

# Web Server Configuration
PORT=3000

# Other Settings
# ALLOWED_LINKS: Use domain names only (without http/https)
# Example: example.com,google.com,github.com
ALLOWED_LINKS=example.com
TIMEZONE_OFFSET=7

# Hell Event Filter Settings
# ONLY_WATCHER_CHAOS: true = only show Watcher/Chaos Dragon events, false = show all events
ONLY_WATCHER_CHAOS=true

# Gemini AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here
