{"dependencies": {"discord.js": "^14.18.0", "dotenv": "^16.4.7", "express": "^4.18.2", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "node-cron": "^3.0.3", "node-fetch": "^2.7.0", "qrcode": "^1.5.3", "qrcode-terminal": "^0.12.0", "socket.io": "^4.7.4", "whatsapp-web.js": "^1.26.0"}, "name": "watcher-chaos-wa-bot", "version": "1.1.0", "description": "WhatsApp bot for receiving Lords Mobile Hell Events in WhatsApp Group. And many more features!", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js"}, "repository": {"type": "git", "url": "git+https://github.com/tupski/watcher-chaos-wabot.git"}, "keywords": ["Lords", "Mobile", "Watcher", "Chaos", "Dragon", "Hell", "Events", "WhatsApp", "Bot"], "author": "<PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/tupski/watcher-chaos-wabot/issues"}, "homepage": "https://github.com/tupski/watcher-chaos-wabot#readme"}