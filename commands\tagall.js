const { getChatInfo } = require('../utils/chatUtils');

/**
 * Tags all participants in a WhatsApp group.
 *
 * @param {Object} client - The WhatsApp client instance.
 * @param {Object} message - The message that triggered the command.
 */
module.exports = async (client, message) => {
    // Handle the !tagall command
    try {
        const chatInfo = await getChatInfo(client, message);

        if (!chatInfo.isGroup) {
            await message.reply('This command can only be used in group chats.');
            return;
        }

        // Extract any message after the command
        const commandParts = message.body.split(' ');
        commandParts.shift(); // Remove the command itself
        const userMessage = commandParts.join(' ');

        // Get participants using the utility function
        const participants = chatInfo.participants;

        if (!participants || participants.length === 0) {
            console.log('No participants found, providing detailed error message');

            const errorMessage =
                '❌ *Unable to Tag All Members*\n\n' +
                '**Issue:** Cannot retrieve group participant list.\n\n' +
                '**Possible causes:**\n' +
                '• WhatsApp Web session needs refresh\n' +
                '• Group metadata not accessible\n' +
                '• WhatsApp Web.js compatibility issue\n\n' +
                '**Solutions:**\n' +
                '1. Run `!debug` command for detailed diagnostics\n' +
                '2. Try again in a few minutes\n' +
                '3. Restart the bot\n' +
                '4. Re-scan QR code if needed\n' +
                '5. Check if WhatsApp Web.js needs updating\n\n' +
                '**Note:** WhatsApp doesn\'t support @everyone like Discord.\n' +
                'Individual mentions require participant data access.';

            await message.reply(errorMessage);
            return;
        }

        console.log(`Found ${participants.length} participants in the group`);

        // Create the mention list
        const mentions = [];
        let mentionText = userMessage ? `*${userMessage}*\n\n` : '*[TAG ALL]*\n\n';

        for (let participant of participants) {
            try {
                // Skip the bot itself
                if (client.info && client.info.wid && participant.id._serialized === client.info.wid._serialized) {
                    continue;
                }

                // Add participant to mention
                if (participant.id && participant.id.user) {
                    mentionText += `@${participant.id.user} `;
                    mentions.push(participant.id._serialized);
                } else {
                    console.log('Skipping participant with invalid ID structure:', participant);
                }
            } catch (error) {
                console.log('Error processing participant:', participant, error.message);
            }
        }

        if (mentions.length === 0) {
            await message.reply('❌ No valid participants found to mention.');
            return;
        }

        console.log(`Mentioning ${mentions.length} participants`);

        try {
            // Send the message with mentions
            await chatInfo.chat.sendMessage(mentionText, {
                mentions: mentions
            });

            console.log('Tag all message sent successfully.');

            // Send confirmation
            await message.reply(`✅ Tagged ${mentions.length} group members successfully!`);

        } catch (sendError) {
            console.error('Error sending tag all message:', sendError);
            await message.reply('❌ Failed to send tag all message. Please try again.');
        }
    } catch (error) {
        console.error('Error executing tagall command:', error);
        await message.reply('An error occurred while executing the command.');
    }
};
