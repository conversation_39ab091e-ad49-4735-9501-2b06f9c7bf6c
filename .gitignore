# Dependencies
/node_modules

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# WhatsApp session files
/.wwebjs_auth
/.wwebjs_cache

# Data and storage
/data/*
!data/.gitkeep
*.json
!package.json
!package-lock.json

# Logs
/logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# IDE files
.idea/
.vscode/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db